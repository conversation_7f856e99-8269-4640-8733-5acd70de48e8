#!/usr/bin/env python3
"""
Python Scripts - Similar to npm scripts
Usage: python scripts.py <command>
"""

import sys
import subprocess

def run_command(cmd):
    """Run a shell command"""
    print(f"🔄 Running: {cmd}")
    result = subprocess.run(cmd, shell=True)
    return result.returncode

def install():
    """Install dependencies - like npm install"""
    print("📦 Installing Python dependencies...")
    return run_command("pip install -r requirements.txt")

def start():
    """Start the Flask API - like npm start"""
    print("🚀 Starting Flask API...")
    return run_command("python flask_evaluator_api.py")

def main():
    if len(sys.argv) < 2:
        print("❌ No command specified")
        help_cmd()
        return 1
    
    command = sys.argv[1]
    
    commands = {
        'install': install,
        'start': start,
        'dev': dev,
        'clean': clean,
        'help': help_cmd
    }
    
    if command in commands:
        return commands[command]()
    else:
        print(f"❌ Unknown command: {command}")
        help_cmd()
        return 1

if __name__ == "__main__":
    sys.exit(main())
