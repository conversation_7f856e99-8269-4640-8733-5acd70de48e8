#!/usr/bin/env python3
"""
Python Scripts - Similar to npm scripts
Usage: python scripts.py <command>
"""

import sys
import subprocess
import os

def run_command(cmd):
    """Run a shell command"""
    print(f"🔄 Running: {cmd}")
    result = subprocess.run(cmd, shell=True)
    return result.returncode

def install():
    """Install dependencies - like npm install"""
    print("📦 Installing Python dependencies...")
    return run_command("pip install -r requirements.txt")

def start():
    """Start the Flask API - like npm start"""
    print("🚀 Starting Flask API...")
    return run_command("python flask_evaluator_api.py")

def dev():
    """Start in development mode - like npm run dev"""
    print("🔧 Starting Flask API in development mode...")
    os.environ['FLASK_ENV'] = 'development'
    return run_command("python flask_evaluator_api.py")

def clean():
    """Clean Python cache files - like npm run clean"""
    print("🧹 Cleaning Python cache files...")
    commands = [
        "find . -type f -name '*.pyc' -delete",
        "find . -type d -name '__pycache__' -delete",
        "find . -type d -name '*.egg-info' -exec rm -rf {} + 2>/dev/null || true"
    ]
    for cmd in commands:
        run_command(cmd)
    return 0

def help_cmd():
    """Show available commands"""
    print("📋 Available commands:")
    print("  python scripts.py install  - Install dependencies")
    print("  python scripts.py start    - Start Flask API")
    print("  python scripts.py dev      - Start in development mode")
    print("  python scripts.py clean    - Clean cache files")
    print("  python scripts.py help     - Show this help")
    return 0

def main():
    if len(sys.argv) < 2:
        print("❌ No command specified")
        help_cmd()
        return 1
    
    command = sys.argv[1]
    
    commands = {
        'install': install,
        'start': start,
        'dev': dev,
        'clean': clean,
        'help': help_cmd
    }
    
    if command in commands:
        return commands[command]()
    else:
        print(f"❌ Unknown command: {command}")
        help_cmd()
        return 1

if __name__ == "__main__":
    sys.exit(main())
