#!/usr/bin/env python3
"""
Python Evaluator API Management Scripts
Usage: python scripts.py <command>
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, shell=True):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=shell, check=True, capture_output=True, text=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        return False

def create_venv():
    """Create virtual environment"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("📦 Virtual environment already exists")
        return True
    
    print("📦 Creating virtual environment...")
    if run_command(f"{sys.executable} -m venv venv"):
        print("✅ Virtual environment created successfully!")
        return True
    else:
        print("❌ Failed to create virtual environment")
        return False

def get_pip_command():
    """Get the correct pip command for the current platform"""
    if os.name == 'nt':  # Windows
        return "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        return "venv/bin/pip"

def get_python_command():
    """Get the correct python command for the current platform"""
    if os.name == 'nt':  # Windows
        return "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        return "venv/bin/python"

def install():
    """Install dependencies in virtual environment"""
    if not Path("venv").exists():
        if not create_venv():
            return 1
    
    print("📦 Installing Python dependencies in virtual environment...")
    pip_cmd = get_pip_command()
    
    if run_command(f"{pip_cmd} install --upgrade pip"):
        print("✅ Pip upgraded successfully!")
    
    if run_command(f"{pip_cmd} install -r requirements.txt"):
        print("✅ Dependencies installed successfully!")
        return 0
    else:
        print("❌ Failed to install dependencies")
        return 1

def dev():
    """Start the API in development mode using virtual environment"""
    if not Path("venv").exists():
        print("❌ Virtual environment not found. Run 'python scripts.py install' first.")
        return 1
    
    print("🚀 Starting Evaluator API in development mode...")
    python_cmd = get_python_command()
    
    # Run the Flask app directly (no subprocess, so we can see output)
    os.system(f"{python_cmd} flask_evaluator_api.py")
    return 0

def start():
    """Start the API server using virtual environment"""
    return dev()  # Same as dev for now

def clean():
    """Clean cache files and virtual environment"""
    print("🧹 Cleaning cache files...")
    
    # Clean Python cache files
    for root, dirs, files in os.walk('.'):
        # Remove __pycache__ directories
        for d in dirs[:]:  # Use slice to avoid modifying list while iterating
            if d == '__pycache__':
                cache_path = os.path.join(root, d)
                shutil.rmtree(cache_path, ignore_errors=True)
                print(f"  Removed: {cache_path}")
        
        # Remove .pyc files
        for f in files:
            if f.endswith(('.pyc', '.pyo')):
                file_path = os.path.join(root, f)
                os.remove(file_path)
                print(f"  Removed: {file_path}")
    
    print("✅ Cache files cleaned!")
    return 0

def clean_all():
    """Clean everything including virtual environment"""
    clean()
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("🗑️  Removing virtual environment...")
        shutil.rmtree(venv_path, ignore_errors=True)
        print("✅ Virtual environment removed!")
    
    return 0

def activate_help():
    """Show how to activate virtual environment"""
    print("🔧 To manually activate the virtual environment:")
    if os.name == 'nt':  # Windows
        print("  venv\\Scripts\\activate")
    else:  # Unix/Linux/macOS
        print("  source venv/bin/activate")
    print("\n🔧 To deactivate:")
    print("  deactivate")

def help_command():
    """Show available commands"""
    print("📋 Available commands:")
    print("  python scripts.py install    - Create venv and install dependencies")
    print("  python scripts.py dev        - Start API in development mode")
    print("  python scripts.py start      - Start API server")
    print("  python scripts.py clean      - Clean cache files")
    print("  python scripts.py clean-all  - Clean cache files and remove venv")
    print("  python scripts.py activate   - Show how to activate venv manually")
    print("  python scripts.py help       - Show this help message")
    return 0

def main():
    """Main function"""
    if len(sys.argv) < 2:
        help_command()
        return 1
    
    command = sys.argv[1].lower()
    
    commands = {
        'install': install,
        'dev': dev,
        'start': start,
        'clean': clean,
        'clean-all': clean_all,
        'activate': activate_help,
        'help': help_command,
    }
    
    if command in commands:
        return commands[command]()
    else:
        print(f"❌ Unknown command: {command}")
        help_command()
        return 1

if __name__ == "__main__":
    sys.exit(main())
