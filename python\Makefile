# Python Evaluator API Makefile
# Usage: make <command>

.PHONY: install dev start clean test lint format help

# Default target
help:
	@echo "Available commands:"
	@echo "  make install    - Install Python dependencies"
	@echo "  make dev        - Start API in development mode"
	@echo "  make start      - Start API server"
	@echo "  make clean      - Clean cache files and bytecode"
	@echo "  make test       - Run tests (if available)"
	@echo "  make lint       - Run linting (if flake8 installed)"
	@echo "  make format     - Format code (if black installed)"
	@echo "  make help       - Show this help message"

# Install dependencies
install:
	@echo "📦 Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "✅ Dependencies installed successfully!"

# Start in development mode
dev:
	@echo "🚀 Starting Evaluator API in development mode..."
	python flask_evaluator_api.py

# Start server
start:
	@echo "🚀 Starting Evaluator API server..."
	python flask_evaluator_api.py

# Clean cache files
clean:
	@echo "🧹 Cleaning cache files..."
	find . -type d -name "__pycache__" -delete 2>/dev/null || true
	find . -name "*.pyc" -delete 2>/dev/null || true
	find . -name "*.pyo" -delete 2>/dev/null || true
	find . -name "*.pyd" -delete 2>/dev/null || true
	find . -name ".coverage" -delete 2>/dev/null || true
	find . -name "*.cover" -delete 2>/dev/null || true
	find . -name "*.log" -delete 2>/dev/null || true
	@echo "✅ Cache files cleaned!"

# Run tests (placeholder)
test:
	@echo "🧪 Running tests..."
	@echo "⚠️  No tests configured yet"

# Run linting
lint:
	@echo "🔍 Running linting..."
	@if command -v flake8 >/dev/null 2>&1; then \
		flake8 *.py; \
	else \
		echo "⚠️  flake8 not installed. Install with: pip install flake8"; \
	fi

# Format code
format:
	@echo "🎨 Formatting code..."
	@if command -v black >/dev/null 2>&1; then \
		black *.py; \
	else \
		echo "⚠️  black not installed. Install with: pip install black"; \
	fi
