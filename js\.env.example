# Application
PORT=5002
NODE_ENV='dev'
JWT_SECRET=''
APP_URL='http://localhost:3000'
API_URL='http://localhost:5001/api'

# AWS
AWS_ACCESS_KEY_ID=''
AWS_SECRET_ACCESS_KEY=''

AWS_COMPRESSED_ITEMS_BUCKET=''
AWS_COMPRESSED_ITEMS_REGION=''

# MQTT
MQTT_CLIENT_ID=''
MQTT_BROKER=''

# Database
MONGO_URI=''

# NodeMailer Settings for Google Account
MAIL_USER=''
CLIENT_ID=''
CLIENT_SECRET=''
REDIRECT_URI=''
REFRESH_TOKEN=''

# Porkbun Mail Configuration
PORKBUN_MAIL_USER=''
PORKBUN_MAIL_USERNAME=''
PORKBUN_MAIL_PASSWORD=''

# Thingsboard API
THINGSBOARD_API_BASE_URL=''
THINGSBOARD_USERNAME=''
THINGSBOARD_PASSWORD=''
THINGSBOARD_DASHBOARD_ID=''

# Slack
SLACK_LOGS_WEBHOOK_URL = ''