require('dotenv').config();;
const db = require('../modules/db');
const { getDBCollectionNames } = require('../utils/functions');

const BATCH_SIZE = 1000;

async function buildVesselMapping() {
    process.stdout.write('Building vessel mapping cache...');
    try {
        const vessels = await db.qmShared.collection('vessels').find({}, { unit_id: 1, _id: 1 });
        const mapping = {};
        vessels.forEach(vessel => {
            mapping[vessel.unit_id] = vessel._id;
        });
        process.stdout.write(`Built mapping for ${Object.keys(mapping).length} vessels`);
        return mapping;
    } catch (error) {
        console.error('Error building vessel mapping:', error);
        throw error;
    }
}

async function updateLocationCollection(collectionName, onboardVesselId) {
    process.stdout.write(`\nProcessing collection: ${collectionName}\n`);

    if (!onboardVesselId) {
        process.stdout.write(`No vessel mapping found, skipping collection ${collectionName}`);
        return 0;
    }

    const collection = db.qm.collection(collectionName);

    const totalCount = await collection.countDocuments({
        onboardVesselId: { $exists: false }
    });

    if (totalCount === 0) {
        process.stdout.write(`No documents to update in ${collectionName}`);
        return 0;
    }

    process.stdout.write(`Found ${totalCount} documents to update in ${collectionName}\n`);

    let updatedCount = 0;
    let processedCount = 0;

    while (processedCount < totalCount) {
        const result = await collection.updateMany(
            {
                onboardVesselId: { $exists: false }
            },
            {
                $set: { onboardVesselId: onboardVesselId }
            },
            {
                limit: BATCH_SIZE
            }
        );

        updatedCount += result.modifiedCount;
        processedCount += BATCH_SIZE;

        const progress = Math.min(100, Math.round((updatedCount / totalCount) * 100));
        process.stdout.write(`Progress: ${updatedCount}/${totalCount} documents (${progress}%)\n`);

        if (result.modifiedCount === 0) {
            break;
        }
    }

    process.stdout.write(`Completed ${collectionName}: ${updatedCount} documents updated\n`);
    return updatedCount;
}

async function updateOnboardVesselIds() {
    const startTime = Date.now();
    let totalUpdated = 0;

    try {
        process.stdout.write('Starting onboardVesselId migration...');

        const vesselMapping = await buildVesselMapping();

        const locationCollectionsNames = await getDBCollectionNames(db.qmLocations)

        process.stdout.write(`Found ${locationCollectionsNames.length} location collections to process\n`);

        for (const collectionName of locationCollectionsNames) {
            const updatedCount = await updateLocationCollection(collectionName, vesselMapping[collectionName.replace(/_location$/, '')]);
            totalUpdated += updatedCount;
        }

        const timeElapsed = (Date.now() - startTime) / 1000;
        process.stdout.write(`\nMigration completed successfully!`);
        process.stdout.write(`Total documents updated: ${totalUpdated}`);
        process.stdout.write(`Total time: ${timeElapsed.toFixed(2)} seconds`);

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(() => {
    process.stdout.write('Database connections established, starting migration...');
    updateOnboardVesselIds()
        .then(() => {
            process.stdout.write('Migration script completed successfully');
            process.exit(0);
        })
        .catch(err => {
            console.error('Migration script failed:', err);
            process.exit(1);
        });
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});
