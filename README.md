# Quartermaster Web Microservices
## Table of Contents
- [Quartermaster Web Microservices](#quartermaster-web-microservices)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
  - [Architecture Overview](#architecture-overview)
  - [Running the Code](#running-the-code)
    - [Prerequisites](#prerequisites)
    - [Cloning the Repository](#cloning-the-repository)
    - [Project Structure](#project-structure)
    - [Installation](#installation)
    - [Environment Variables](#environment-variables)
    - [Running the Microservices](#running-the-microservices)
## Overview
This repository includes the source code for the microservices that are further integrated with the Quartermaster Web Application. WebSockets are used as the communication interface for these services.
## Architecture Overview
[<img alt="quartermaster_web_architecture" src="assets/quartermaster-microservices-architecture.jpg" />]()
## Running the Code
### Prerequisites
Make sure you have the following installed:

- **Node.js**: v18.x or higher
- **npm**: v8.x or higher
- **Python**: v3.8 or higher
- **pip**: Latest version
- **MongoDB**: v7.x or higher
- **Git**: v2.x or higher

Node.js download link: https://nodejs.org/en/download/package-manager\
Python download link: https://www.python.org/downloads\
MongoDB download link: https://www.mongodb.com/docs/manual/installation\
Git download link: https://git-scm.com/downloads
### Cloning the Repository
1. Acquire the **Personal Access Token (PAT)** from the respository owner in order to clone this repository
2. Clone the repository using the following git command:
```bash
git clone https://<personal-access-token>@github.com/Quartermaster-AI/quartermaster-web-microservices.git
```
- Ensure to replace `<personal-access-token>` with your acquired token (without angular brackets)
### Project Structure
```bash
📦 Project Root
├── 📁 assets                   # Asset files
├── 📁 js                       # JavaScript services
│   ├── 📁 models                   # Database models
│   ├── 📁 modules                  # Server modules
│   │   ├── 📄 awsIot.js            # IoT core module
│   │   ├── 📄 db.js                # Database connection module
│   │   ├── 📄 io.js                # WebSocket module
│   │   ├── 📄 mqttClient.js        # MQTT connection with IoT
│   │   ├── 📄 processLogs.js       # Resource usage logging
│   │   └── 📄 winston.js           # Logger module
│   ├── 📁 scripts              # JS scripts
│   ├── 📁 services             # JS services
│   ├── 📁 utils                # JS utilities
│   ├── 📄 .env                 # JS environment variables
│   ├── 📄 .env.example         # JS sample .env file
│   ├── 📄 .gitignore           # JS-specific gitignore
│   ├── 📄 package.json         # JS dependencies
│   └── 📄 index.js             # JS app entry point
├── 📁 python                   # Python services
│   ├── 📄 evaluator.py         # Artifact evaluator
│   ├── 📄 flask_evaluator_api.py # Flask API
│   ├── 📄 requirements.txt     # Python dependencies
│   ├── � .env                 # Python environment variables
│   ├── 📄 .env.example         # Python sample .env file
│   ├── 📄 .gitignore           # Python-specific gitignore
│   └── 📁 venv/                # Virtual environment (auto-created)
├── 📁 scripts                  # Shared scripts
└── 📄 README.md                # This file
```
### Installation
- Install JavaScript dependencies:
```bash
cd js
npm install
```
- **Create and setup Python virtual environment:**
```bash
cd python

# 1. Create virtual environment
python -m venv venv

# 2. Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. Install dependencies (after activation)
pip install -r requirements.txt
```

**Note:** After activation, your command prompt will show `(venv)` indicating the virtual environment is active. This isolates Python packages from your system installation.
### Environment Variables
Each service has its own environment configuration:

**JavaScript Services:**
- Copy `js/.env.example` to `js/.env`
- Configure JS-specific variables (ports, AWS, database, etc.)

**Python Services:**
- Copy `python/.env.example` to `python/.env`
- Configure Python-specific variables (Flask settings, model config, etc.)

Contact the repository owner for assistance with sensitive values.
### Running the Microservices
Run the following command:

**JavaScript Services:**
```bash
cd js
npm run dev
```
The microservices can be accessed over WebSocket connection using the URL http://localhost:5002/\

**Python Services:**
```bash
cd python

# Activate virtual environment first:
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Then run the API (you should see (venv) in your prompt):
python flask_evaluator_api.py
```
The Python API runs on http://localhost:5003/ (configurable via `FLASK_PORT` in `.env`)

**Deactivating Python Virtual Environment:**
```bash
# To deactivate the virtual environment when done:
deactivate
```

**Testing:**
[Postman](https://www.postman.com/) can be used for quick testing using Socket.io request type for WebSocket services, or standard HTTP requests for the Python API