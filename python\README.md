# Evaluator API

Flask API for artifact evaluation using the evaluator.py script.

## Setup & Usage

```bash
# Install dependencies 
python scripts.py install

# Start the API server 
python scripts.py start

# Start in development mode 
python scripts.py dev

# Clean cache files 
python scripts.py clean

# Show help 
python scripts.py help
```

### API Endpoints

#### POST /evaluate/sequential
Evaluates artifacts sequentially (prev vs next comparison).

**Request:**
```json
{
  "artifacts": [
    {
      "_id": "...",
      "timestamp": "...",
      "image_path": "...",
      "category": "...",
      "super_category": "...",
      "size": "...",
      "imo_number": "...",
      "color": "...",
      "weapons": "...",
      "country_flag": "...",
      "others": "...",
      "text_extraction": [...]
    }
  ]
}
```

**Response:**
```json
[
  {
    "_id": "artifact_id",
    "duplicate_index": 0.856,
    "other_evaluation_scores": "..."
  }
]
```

### Duplicate Detection Logic
- `duplicate_index = 0` when:
  - Same image_path OR
  - Time difference > 30 minutes
- Otherwise: `duplicate_index = average of evaluation scores`

## Commands

```bash
python scripts.py install   # Install dependencies
python scripts.py start     # Start API server
python scripts.py dev       # Start with development mode
python scripts.py clean     # Clean cache files
python scripts.py test      # Run tests
python scripts.py help      # Show available commands
```
