# Evaluator API

Flask API for artifact evaluation using the evaluator.py script.

## Environment Setup

The Python environment uses a `.env` file for configuration, similar to the JavaScript setup.

### Environment Variables

The `.env` file contains the following configuration:

- **Flask Configuration**: `FLASK_ENV`, `FLASK_DEBUG`, `FLASK_PORT`
- **API Configuration**: `API_HOST`, `API_PORT`
- **CORS Configuration**: `CORS_ORIGINS`
- **Model Configuration**: `MODEL_NAME`, `SIMILARITY_THRESHOLD`
- **Logging Configuration**: `LOG_LEVEL`, `LOG_FORMAT`
- **Database**: `MONGO_URI` (shared with JS services)
- **External APIs**: `JS_API_URL`, `MAIN_API_URL`

## Setup & Usage

```bash
# Install dependencies 
python scripts.py install

# Start the API server 
python scripts.py start

# Start in development mode 
python scripts.py dev

# Clean cache files
python scripts.py clean

# Check environment configuration
python scripts.py check-env

# Show help
python scripts.py help
```

### API Endpoints

#### POST /evaluate/sequential
Evaluates artifacts sequentially (prev vs next comparison).

**Request:**
```json
{
  "artifacts": [
    {
      "_id": "...",
      "timestamp": "...",
      "image_path": "...",
      "category": "...",
      "super_category": "...",
      "size": "...",
      "imo_number": "...",
      "color": "...",
      "weapons": "...",
      "country_flag": "...",
      "others": "...",
      "text_extraction": [...]
    }
  ]
}
```

**Response:**
```json
[
  {
    "_id": "artifact_id",
    "duplicate_index": 0.856,
    "other_evaluation_scores": "..."
  }
]
```

### Duplicate Detection Logic
- `duplicate_index = 0` when:
  - Same image_path OR
  - Time difference > 30 minutes
- Otherwise: `duplicate_index = average of evaluation scores`

## Commands

```bash
python scripts.py install   # Install dependencies
python scripts.py start     # Start API server
python scripts.py dev       # Start with development mode
python scripts.py clean     # Clean cache files
python scripts.py test      # Run tests
python scripts.py lint      # Run linting
python scripts.py format    # Format code
python scripts.py check-env # Check environment configuration
python scripts.py help      # Show available commands
```

## Dependencies

Dependencies are managed through:
- `requirements.txt` - for pip installation
- `pyproject.toml` - for modern Python packaging

Key dependencies:
- Flask - Web framework
- flask-cors - CORS support
- sentence-transformers - ML model
- python-dotenv - Environment variable loading

The API will start on `http://localhost:5003` by default (configurable via `API_PORT` in `.env`).
