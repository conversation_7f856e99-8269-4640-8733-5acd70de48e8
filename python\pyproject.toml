[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "evaluator-api"
version = "1.0.0"
description = "Flask API for artifact evaluation"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "Flask==3.1.1",
    "flask-cors==6.0.1",
    "numpy==1.26.4",
    "sentence-transformers==3.3.1",
    "python-dotenv==1.0.0"
]

[project.scripts]
start = "flask_evaluator_api:main"
dev = "flask_evaluator_api:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]
