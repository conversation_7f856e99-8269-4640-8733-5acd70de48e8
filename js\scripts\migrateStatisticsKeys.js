// Script to migrate statistics documents: remove all v1 keys and rename v2 keys to v1
// Usage: node scripts/migrateStatisticsKeys.js
require('dotenv').config({ path: require('path').resolve(__dirname, '../../.env') });;
const Statistics = require('../models/Statistics');
const db = require('../modules/db');

async function migrateStatisticsKeys() {
    try {
        const statsDocs = await Statistics.find({});
        let updatedCount = 0;
        let checkedCount = 0;
        console.log(`Found ${statsDocs.length} statistics documents to process.`);

        for (const doc of statsDocs) {
            let modified = false;
            const stats = doc.stats;

            // List of keys to migrate
            const keyPairs = [
                ['totalVesselsDetectedbySensorsV2', 'totalVesselsDetectedbySensors'],
                ['totalSensorsDurationAtSeaV2', 'totalSensorsDurationAtSea'],
                ['totalSensorsOnlineDurationV2', 'totalSensorsOnlineDuration'],
                ['totalSmartmastsDistanceTraveledV2', 'totalSmartmastsDistanceTraveled']
            ];

            // Remove v1 keys
            keyPairs.forEach(([v2, v1]) => {
                if (stats[v1] !== undefined) {
                    console.log(`[${doc._id}] Removing key: ${v1}`);
                    delete stats[v1];
                    modified = true;
                }
            });

            // Rename v2 to v1
            keyPairs.forEach(([v2, v1]) => {
                if (stats[v2] !== undefined) {
                    console.log(`[${doc._id}] Renaming key: ${v2} -> ${v1}`);
                    stats[v1] = stats[v2];
                    delete stats[v2];
                    modified = true;
                }
            });

            checkedCount++;
            if (modified) {
                doc.stats = stats;
                doc.markModified('stats');
                await doc.save();
                updatedCount++;
                console.log(`[${doc._id}] Document updated and saved. (${updatedCount} so far)`);
            } else {
                console.log(`[${doc._id}] No changes needed.`);
            }
        }
        console.log(`Migration complete. Updated ${updatedCount} of ${checkedCount} documents.`);
        process.exit(0);
    } catch (err) {
        console.error('Migration failed:', err);
        process.exit(1);
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    })
]).then(() => {
    migrateStatisticsKeys()
        .then(() => console.log('Migration completed successfully'))
        .catch(err => console.error('Migration failed:', err));
});
migrateStatisticsKeys();
